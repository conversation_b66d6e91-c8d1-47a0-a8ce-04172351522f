import os.path

from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QColor, QFont
from PySide6.QtWidgets import QFrame, QSizePolicy, QLabel, QVBoxLayout, QPushButton, QWidget, QHB<PERSON>Layout, QBoxLayout

from .vtk_urdf_viewer.urdf_viewer import VT<PERSON><PERSON><PERSON>Viewer
from ..components.base_widgets import BorderlessLayout
from ..ui.hand_sim_viz_panel import Ui_Form
from ... import APP_ROOT_PATH
from ...config import LEFT_URDF_PATH, RIGHT_URDF_PATH
from ...utils.resource_helper import get_image


_LABEL_NAME_QSS = """
QLabel {
    background-color: transparent;
    color: white;
    border: none;
    font-family: 'Alibaba PuHuiTi 2.0';
    font-size: 24px;
    font-weight: 300;
}
"""

_LABEL_TIP_QSS = """
QLabel {
    background-color: transparent;
    color: gray;
    border: none;
    font-family: 'Alibaba PuHuiTi 2.0';
    font-size: 16px;
    font-weight: 300;
}
"""

_CONNECT_BUTTON_QSS = """
font-family: 'Alibaba PuHuiTi 2.0';
font-weight: 300;
font-size: 20px;
background-color: #FF9429;
color: black;
border: none;
border-radius: 8px;
"""


class StatusButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)

        self.dot_color = QColor("#2DC96E")
        self.setFixedSize(120, 40)

        self.setStyleSheet("""
            QPushButton {
                background-color: #35363A;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 16px;
                font-weight: 300;
                letter-spacing: 0px;  /* 可能不支持，依赖平台 */
            }
        """)

    def paintEvent(self, event):
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Get the tab rect
        rect = self.rect()

        # Set the dot color
        painter.setBrush(self.dot_color)
        painter.setPen(Qt.NoPen)

        # Draw the dot
        dot_size = 12
        dot_x = rect.left() + 5
        dot_y = rect.center().y() - dot_size // 2
        painter.drawEllipse(dot_x, dot_y, dot_size, dot_size)


class HandSimVizWidget(QFrame, Ui_Form):
    switch_hand_type_signal = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self._hand_version_buttons = []

        self._init_ui()

    def _init_ui(self):
        self.tabWidget_model_left.tabBar().hide()
        self.tabWidget_model_right.tabBar().hide()

        # 设置主界面布局
        self.layout().setContentsMargins(20, 40, 20, 20)
        self.setStyleSheet("background-color: #2E3034; border: 1px; border-radius: 5px;")

        self.label_sh_left.setFixedSize(40, 40)
        self.label_sh_left.setStyleSheet("border-radius: 8px; background-color: transparent;")
        pix = get_image("vector.svg")
        pix = pix.scaled(28, 28, Qt.AspectRatioMode.KeepAspectRatio)
        self.label_sh_left.setPixmap(pix)
        self.label_sh_right.setFixedSize(40, 40)
        self.label_sh_right.setStyleSheet("border-radius: 8px; background-color: transparent;")
        pix = get_image("vector.svg")
        pix = pix.scaled(28, 28, Qt.AspectRatioMode.KeepAspectRatio)
        self.label_sh_right.setPixmap(pix)

        # 状态设置
        self.horizontalLayout_head_left.setContentsMargins(10, 0, 10, 0)
        self.horizontalLayout_head_left.setSpacing(0)
        self.status_button_left = StatusButton(self.tr("Connected"), self)
        self.horizontalLayout_head_left.insertWidget(-1, self.status_button_left)
        self.horizontalLayout_head_right.setContentsMargins(10, 0, 10, 0)
        self.horizontalLayout_head_right.setSpacing(0)
        self.status_button_right = StatusButton(self.tr("Connected"), self)
        self.horizontalLayout_head_right.insertWidget(-1, self.status_button_right)

        # 名称标签
        self.label_name_left.setFixedHeight(40)
        self.label_name_left.setStyleSheet(_LABEL_NAME_QSS)
        self.label_name_right.setFixedHeight(40)
        self.label_name_right.setStyleSheet(_LABEL_NAME_QSS)

        # tip
        self.label_tip.setStyleSheet(_LABEL_TIP_QSS)

        # 切换
        self.widget_hand_type = QWidget(self)
        self.widget_hand_type.setLayout(BorderlessLayout(QBoxLayout.Direction.LeftToRight))

        self.widget_hand_type.setFixedSize(332, 48)
        self.widget_hand_type.setStyleSheet("border: none; border-radius: 8px; padding: 4 4 4 4;background-color: #444345;")
        self.widget_hand_type.setLayout(QHBoxLayout())
        self.widget_hand_type.layout().setSpacing(0)
        self.widget_hand_type.layout().setContentsMargins(4, 0, 4, 0)
        for hand_type in ["Left", "Right", "Both"]:
            button = QPushButton(hand_type)
            button.setFixedHeight(40)
            button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
            button.setCheckable(True)
            button.setStyleSheet("""
            QPushButton {
                font-family: 'Alibaba PuHuiTi 2.0';
                font-weight: 300;
                font-size: 20px;
                background-color: #3F4749;
                color: white;
                border: none;
            }
            QPushButton:checked {
                border: none solid white;
                background-color: #52595A;
            }
            """)
            self._hand_version_buttons.append(button)
            self.widget_hand_type.layout().addWidget(button)
            button.clicked.connect(self._on_hand_type_button_clicked)

        self.verticalLayout_switch.addWidget(self.widget_hand_type)

        # 连接按钮
        self.pushButton_connect_left.setFixedSize(200, 58)
        self.pushButton_connect_left.setText("Connect")
        self.pushButton_connect_left.setStyleSheet(_CONNECT_BUTTON_QSS)
        self.pushButton_connect_right.setFixedSize(200, 58)
        self.pushButton_connect_right.setText("Connect")
        self.pushButton_connect_right.setStyleSheet(_CONNECT_BUTTON_QSS)

        # URDF可视化
        self.left_urdf_viewer = VTKURDFViewer(self)
        self.right_urdf_viewer = VTKURDFViewer(self)
        self.verticalLayout_urdf_left.addWidget(self.left_urdf_viewer)
        self.verticalLayout_urdf_right.addWidget(self.right_urdf_viewer)
        # self.left_urdf_viewer.loading_finished.connect(self.on_loading_finished)
        # self.right_urdf_viewer.loading_finished.connect(self.on_loading_finished)
        QTimer.singleShot(50, self._load_default_urdf)

    def _load_default_urdf(self):
        self.left_urdf_viewer.load_urdf(os.path.join(APP_ROOT_PATH, *LEFT_URDF_PATH))
        self.left_urdf_viewer.load_urdf(os.path.join(APP_ROOT_PATH, *RIGHT_URDF_PATH))

    def _on_hand_type_button_clicked(self):
        self.switch_hand_type_signal.emit(self.sender().text())

    def switch_hand_type_button(self, hand_type_str: str):
        for button in self._hand_version_buttons:
            if button.text().lower() == hand_type_str.lower():
                button.setChecked(True)
            else:
                button.setChecked(False)

    def current_hand_type(self):
        for button in self._hand_version_buttons:
            if button.isChecked():
                return button.text()

        # 一个都没选择， 则默认选择所有
        return "Both"

    def set_both_button_visible(self, visible: bool):
        for button in self._hand_version_buttons:
            if button.text() == "Both":
                button.setVisible(visible)

    def set_cur_hand_tab_widget_visible(self, hand_type: str):
        left_widgets = [
            self.tabWidget_model_left,
            self.label_name_left,
            self.label_sh_left,
            self.status_button_left
        ]
        right_widgets = [
            self.tabWidget_model_right,
            self.label_name_right,
            self.label_sh_right,
            self.status_button_right
        ]

        show_left = hand_type != "Right"
        show_right = hand_type != "Left"

        for widget in left_widgets:
            widget.setVisible(show_left)
        for widget in right_widgets:
            widget.setVisible(show_right)

    def __del__(self):
        self.left_urdf_viewer.cleanup_resources()
        self.right_urdf_viewer.cleanup_resources()