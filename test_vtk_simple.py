#!/usr/bin/env python3
"""
简单的VTK测试脚本，用于验证VTK环境配置
"""

import os
import sys

# 设置VTK环境变量
os.environ['VTK_USE_OSMESA'] = '1'
os.environ['MESA_GL_VERSION_OVERRIDE'] = '3.3'
os.environ['VTK_SILENCE_GET_VOID_POINTER_WARNINGS'] = '1'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt, QTimer
import vtk
from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor


class SimpleVTKTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("简单VTK测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 配置VTK全局设置
        self._configure_vtk()
        
        # 创建VTK widget
        self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
        layout.addWidget(self.vtk_widget)
        
        # 设置VTK场景
        self._setup_vtk_scene()
        
        # 延迟显示
        QTimer.singleShot(100, self._show_scene)
    
    def _configure_vtk(self):
        """配置VTK设置"""
        try:
            # 禁用VTK警告
            vtk.vtkObject.GlobalWarningDisplayOff()
            print("VTK全局设置已配置")
        except Exception as e:
            print(f"配置VTK时出错: {e}")
    
    def _setup_vtk_scene(self):
        """设置VTK场景"""
        try:
            # 获取渲染窗口
            self.render_window = self.vtk_widget.GetRenderWindow()
            
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.2, 0.3, 0.4)
            self.render_window.AddRenderer(self.renderer)
            
            # 创建一个简单的立方体
            cube_source = vtk.vtkCubeSource()
            cube_mapper = vtk.vtkPolyDataMapper()
            cube_mapper.SetInputConnection(cube_source.GetOutputPort())
            
            cube_actor = vtk.vtkActor()
            cube_actor.SetMapper(cube_mapper)
            cube_actor.GetProperty().SetColor(1.0, 0.5, 0.0)  # 橙色
            
            self.renderer.AddActor(cube_actor)
            
            # 获取交互器
            self.interactor = self.render_window.GetInteractor()
            
            # 设置交互样式
            style = vtk.vtkInteractorStyleTrackballCamera()
            self.interactor.SetInteractorStyle(style)
            
            print("VTK场景设置完成")
            
        except Exception as e:
            print(f"设置VTK场景时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _show_scene(self):
        """显示场景"""
        try:
            # 初始化交互器
            if hasattr(self, 'interactor') and self.interactor:
                if not self.interactor.GetInitialized():
                    self.interactor.Initialize()
                    print("交互器已初始化")
            
            # 重置相机
            if hasattr(self, 'renderer') and self.renderer:
                self.renderer.ResetCamera()
            
            # 渲染
            if hasattr(self, 'render_window') and self.render_window:
                self.render_window.Render()
                print("场景已渲染")
                
        except Exception as e:
            print(f"显示场景时出错: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    # 设置Qt应用属性
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)
    
    app = QApplication(sys.argv)
    
    window = SimpleVTKTest()
    window.show()
    
    sys.exit(app.exec())
