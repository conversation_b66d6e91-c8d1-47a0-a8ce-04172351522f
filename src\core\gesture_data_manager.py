import json
import os
from typing import Dict, List, Optional, Any
from copy import deepcopy

from src.utils.config_loader import get_config, save_config


DEFAULT_GESTURE_NAMES = ["Open", "Fist", "Precision Pinch", "Tripod Pinch", "Lateral Pinch", "Index Point"]


class StepData:
    def __init__(self, index, duration, mode, positions, speeds, currents):
        self.index = index
        self.duration = duration
        self.mode = mode
        self.positions = positions
        self.speeds = speeds
        self.currents = currents

    def to_list(self):
        expect_interval = [1, 1, 1, 1, 1, 1]   # 默认添加时间参数 expect_interval
        return [self.index, self.duration, self.mode] + self.positions + expect_interval + self.speeds + self.currents


class GestureData:
    def __init__(self, action_id, name, is_custom):
        self.is_custom = is_custom
        self.action_id = action_id
        self.name = name
        self.steps = []

    def steps_to_list(self):
        return [step.to_list() for step in self.steps]

    def __str__(self):
        info = f"name: {self.name}  steps: {len(self.steps)}"
        return info


class GestureDataManager:
    def __init__(self):
        self._gesture_data: Dict[str, GestureData] = {}   # name: GestureData

        self.step_input_range = {"positions": [], "speeds": [], "force": []}
        self.current_sn = None

    def load_data(self, sn, action_sequences: dict):
        self.current_sn = sn

        try:
            gesture_config = get_config(f"Gestures.{sn}")
        except KeyError:
            # 当前设备未连接并保存过配置信息
            gesture_config = {}

        for action_id, action_sequence in action_sequences.items():
            if action_id.value < 7:
                action_name = DEFAULT_GESTURE_NAMES[action_id.value - 1]
            else:
                action_name = gesture_config.get(action_id.value, f"Action_{action_id.value}")
            gesture_data = GestureData(action_id.value, action_name, action_id.value >= 7)
            for step in action_sequence.data:
                # 确保列表数据是拷贝而不是引用
                gesture_data.steps.append(StepData(
                    step.index,
                    step.duration,
                    step.mode,
                    list(step.positions),
                    list(step.speeds),
                    list(step.currents)
                ))

            self._gesture_data[action_name] = gesture_data

    def set_step_input_range(self, min_positions, max_positions, min_speeds, max_speeds, min_currents, max_currents):
        for i in range(6):
            self.step_input_range["positions"].append((min_positions[i], max_positions[i]))
            self.step_input_range["speeds"].append((min_speeds[i], max_speeds[i]))
            self.step_input_range["force"].append((min_currents[i], max_currents[i]))

    def _save_data(self):
        config = get_config("All")
        if config["Gestures"] is None:
            config["Gestures"] = {self.current_sn: {}}
        else:
            config["Gestures"][self.current_sn] = {}

        for gesture_name, gesture_data in self._gesture_data.items():
            # 默认手势不支持改名
            if gesture_data.action_id < 7:
                continue
            config["Gestures"][self.current_sn][gesture_data.action_id] = gesture_name

        save_config(config)

    def get_gesture_data(self, gesture_name: str):
        return deepcopy(self._gesture_data.get(gesture_name))
    
    def get_gesture_steps(self, gesture_name: str):
        gesture_data = self._gesture_data.get(gesture_name)
        if gesture_data:
            return deepcopy(gesture_data.steps)
        return None
    
    def save_gesture_data(self, updated_data: GestureData, old_name: str = None):
        # 如果提供了旧名称且与新名称不同，需要删除旧条目
        if old_name and old_name != updated_data.name and old_name in self._gesture_data:
            del self._gesture_data[old_name]

        # 创建深拷贝以避免引用共享问题
        self._gesture_data[updated_data.name] = deepcopy(updated_data)
        self._save_data()

    def delete_gesture(self, gesture_name: str):
        if gesture_name in self._gesture_data:
            gesture_data = self._gesture_data[gesture_name]
            del self._gesture_data[gesture_name]
            self._save_data()
            return True
        return False
    
    def get_all_gestures(self):
        return deepcopy(self._gesture_data)
    
    def get_custom_gestures(self):
        custom_gestures = {}
        for name, data in self._gesture_data.items():
            if data.is_custom:
                custom_gestures[name] = deepcopy(data)
        return custom_gestures

    def get_used_custom_action_ids(self) -> List[int]:
        used_ids = []
        for name, data in self._gesture_data.items():
            if data.is_custom and data.action_id >= 7:
                used_ids.append(data.action_id)
        return sorted(used_ids)

    def get_next_available_action_id(self) -> int:
        used_ids = self.get_used_custom_action_ids()

        # 在7-30范围内找到第一个未使用的ID
        for action_id in range(7, 31):
            if action_id not in used_ids:
                return action_id

        # 如果所有ID都被使用，返回None或抛出异常
        return None
    
    def get_default_gestures(self) -> Dict[str, Dict[str, Any]]:
        pass
        # default_gestures = {}
        # for name, data in self._gesture_data.items():
        #     if not data.get('is_custom', True):
        #         default_gestures[name] = deepcopy(data)
        # return default_gestures
    
    def rename_gesture(self, old_name: str, new_name: str) -> bool:
        if old_name in self._gesture_data and new_name not in self._gesture_data:
            gesture_data = self._gesture_data[old_name]
            if gesture_data.get('is_custom', True):  # 只能重命名自定义手势
                gesture_data['name'] = new_name
                self._gesture_data[new_name] = gesture_data
                del self._gesture_data[old_name]
                self._save_data()
                return True
        return False


# 全局手势数据管理器实例
_gesture_manager = None


def get_gesture_manager() -> GestureDataManager:
    global _gesture_manager
    if _gesture_manager is None:
        _gesture_manager = GestureDataManager()
    return _gesture_manager
