#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os.path
import sys
import traceback
import time

from PySide6.QtCore import QSize, Qt, QTimer, QCoreApplication
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import (
    QA<PERSON>lication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox,
    QGroupBox, QSpinBox, QTextEdit, QStackedWidget, QSpacerItem, QSizePolicy,
    QMessageBox, QSplashScreen
)
from loguru import logger

from src import APP_ROOT_PATH
from src.gui.main_window import AppMainWindow
from src.utils.config_loader import register_parameters_to_global
from src.utils.loguru_settings import setup_loguru
from src.utils.resource_helper import print_resource_debug_info, get_image
from src.utils.font_manager import setup_application_fonts


def exception_hook(exc_type, exc_value, exc_traceback):
    """全局异常处理函数"""
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"发生错误:\n{error_msg}")
    
    # 将错误写入文件
    with open("error_log.txt", "a", encoding="utf-8") as f:
        f.write(f"\n--- {os.path.basename(__file__)} 错误 ---\n")
        f.write(error_msg)
        f.write("\n--- 错误结束 ---\n")
    
    # 如果Qt应用已经初始化，显示错误对话框
    if QApplication.instance():
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText("程序发生错误")
        msg_box.setInformativeText("详细错误信息已写入error_log.txt文件")
        msg_box.setDetailedText(error_msg)
        msg_box.exec()
    
    # 调用原始的异常处理器
    sys.__excepthook__(exc_type, exc_value, exc_traceback)


def create_splash_screen():
    # 尝试加载启动画面图像
    splash_pixmap = get_image("app_logo.png")
    
    # 如果无法加载图像，创建一个纯色的启动画面
    if splash_pixmap.isNull():
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(Qt.darkGray)
    
    splash = QSplashScreen(splash_pixmap)
    splash.showMessage("正在加载应用...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
    return splash


def delayed_import():
    try:
        from src.gui.main_window import AppMainWindow
        return AppMainWindow
    except ImportError as e:
        print(f"无法导入主窗口模块: {e}")
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 设置全局异常处理
    sys.excepthook = exception_hook
    
    try:
        setup_loguru(log_folder_path="log", show_on_terminal=True)
        logger.info("============================== Programme Start ==============================")

        # QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)

        # 初始化Qt应用
        app = QApplication(sys.argv)
        
        # # 显示启动画面
        # splash = create_splash_screen()
        # splash.show()
        # app.processEvents()

        register_parameters_to_global(os.path.join(APP_ROOT_PATH, "settings.yaml"))

        # 设置应用样式
        app.setStyle("Fusion")

        # 加载并设置全局字体
        setup_application_fonts(app)

        # # 延迟导入主窗口类
        # AppMainWindow = delayed_import()
        # if not AppMainWindow:
        #     QMessageBox.critical(None, "错误", "无法加载主窗口模块，程序将退出")
        #     sys.exit(1)
            
        # 延迟创建主窗口
        def show_main_window():
            try:
                window = AppMainWindow()
                window.show()
                # splash.finish(window)
            except Exception as e:
                logger.error(f"创建主窗口时出错: {e}")
                traceback.print_exc()
                QMessageBox.critical(None, "错误", f"创建主窗口时出错: {str(e)}")
                sys.exit(1)

        # 使用计时器延迟创建主窗口，给资源加载一些时间
        QTimer.singleShot(20, show_main_window)
        
        logger.info("============================== Programme End ==============================")
        sys.exit(app.exec())
    except Exception as e:
        # 捕获所有异常
        traceback.print_exc()
        with open("startup_error.txt", "w", encoding="utf-8") as f:
            f.write(f"启动错误: {str(e)}\n")
            f.write(traceback.format_exc())
        print(f"程序启动失败，错误已写入startup_error.txt: {str(e)}")
        
        if QApplication.instance():
            QMessageBox.critical(None, "启动错误", f"程序启动失败: {str(e)}")
        
        sys.exit(1)
